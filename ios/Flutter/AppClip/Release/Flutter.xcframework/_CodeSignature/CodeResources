<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/Flutter.framework/Flutter</key>
		<data>
		USGecCVHH4X3qxS9WKyqc9TT0qo=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>ios-arm64/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>ios-arm64/Flutter.framework/Info.plist</key>
		<data>
		X2xbdmbtPYDLV7eeRvl3/zwaON8=
		</data>
		<key>ios-arm64/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>ios-arm64/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>ios-arm64/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		Y/repUi0ME4y+D1SK1TEmxPmfrM=
		</data>
		<key>ios-arm64/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>ios-arm64/dSYMs/Flutter.framework.dSYM/Contents/Info.plist</key>
		<data>
		0O4xh8BRc5IjJjTTGIxrSi7YjyA=
		</data>
		<key>ios-arm64/dSYMs/Flutter.framework.dSYM/Contents/Resources/DWARF/Flutter</key>
		<data>
		QVoQTVeqFKsyEuT2mZzkW3E67Dw=
		</data>
		<key>ios-arm64/dSYMs/Flutter.framework.dSYM/Contents/Resources/Relocations/aarch64/Flutter.yml</key>
		<data>
		aTyTWvz5R3ph4wSAbGBDu7sfAuY=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Flutter</key>
		<data>
		Y5bNRHLLy5Ix0wN0uJlSsxfbHxI=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		bkw+DmHReHDg1PPcvmSjuLZrheA=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		LDr6kSVbUfyQFAxLwCACF5S2VEA=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Info.plist</key>
		<data>
		X2xbdmbtPYDLV7eeRvl3/zwaON8=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		Y/repUi0ME4y+D1SK1TEmxPmfrM=
		</data>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/Flutter.framework/Flutter</key>
		<dict>
			<key>hash</key>
			<data>
			USGecCVHH4X3qxS9WKyqc9TT0qo=
			</data>
			<key>hash2</key>
			<data>
			IqSWl/6h0ZPSKElMSvkOkNB4VbcLvl/RMK353rD6mX0=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash</key>
			<data>
			wTPJHICwW6wxY3b87ek7ITN5kJk=
			</data>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			zbvYFr9dywry0lMMrHuNOOaNgkY=
			</data>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash</key>
			<data>
			ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
			</data>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			V/wkSSsyYdMoexF6wPrC3KgkL4g=
			</data>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash</key>
			<data>
			vFsZXNqjflvqKqAzsIptQaTSJho=
			</data>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash</key>
			<data>
			sUgX1PJzkvyinL5i7nS1ro/Kd5o=
			</data>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash</key>
			<data>
			SpNs7IhIC7xP34Ej+LQCaEZkqik=
			</data>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash</key>
			<data>
			AqVvCbPmgWMQKrRnib05Okrjbp0=
			</data>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash</key>
			<data>
			bkw+DmHReHDg1PPcvmSjuLZrheA=
			</data>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash</key>
			<data>
			UqnnVWwQEYYX56eu7lt6dpR3LIc=
			</data>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			VjAwScWkWWSrDeetip3K4yhuwDU=
			</data>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			crQ9782ULebLQfIR+MbBkjB7d+k=
			</data>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash</key>
			<data>
			ocQVSiAiUMYfVtZIn48LpYTJA5w=
			</data>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			EARXud6pHb7ZYP8eXPDnluMqcXk=
			</data>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			qWHw5VIWEa0NmJ1PMhD16nlfRKk=
			</data>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash</key>
			<data>
			31prWLso2k5PfMMSbf5hGl+VE6Y=
			</data>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			LDr6kSVbUfyQFAxLwCACF5S2VEA=
			</data>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			X2xbdmbtPYDLV7eeRvl3/zwaON8=
			</data>
			<key>hash2</key>
			<data>
			DtNp7a85dqmCAARl8dpbKa/0aA+1tosbQfbdRZw+mb4=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wJV5dCKEGl+FAtDc8wJJh/fvKXs=
			</data>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			D+cqXttvC7E/uziGjFdqFabWd7A=
			</data>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			Y/repUi0ME4y+D1SK1TEmxPmfrM=
			</data>
			<key>hash2</key>
			<data>
			Yby50O9fmEEOVwi4gvGkbeU5vkgauDFwso8052Syx3s=
			</data>
		</dict>
		<key>ios-arm64/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash</key>
			<data>
			ipm8hg7aB3LzsfShJfpNR0QQ4hw=
			</data>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/Flutter.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			0O4xh8BRc5IjJjTTGIxrSi7YjyA=
			</data>
			<key>hash2</key>
			<data>
			2sWnxBJViOG2QclRoxjBIR4RqBpdGHQVbDVsz6WEC24=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/Flutter.framework.dSYM/Contents/Resources/DWARF/Flutter</key>
		<dict>
			<key>hash</key>
			<data>
			QVoQTVeqFKsyEuT2mZzkW3E67Dw=
			</data>
			<key>hash2</key>
			<data>
			IvSD34a18OcTRTYuwujuwiwe6EVONqs8wxH0n8yqor8=
			</data>
		</dict>
		<key>ios-arm64/dSYMs/Flutter.framework.dSYM/Contents/Resources/Relocations/aarch64/Flutter.yml</key>
		<dict>
			<key>hash</key>
			<data>
			aTyTWvz5R3ph4wSAbGBDu7sfAuY=
			</data>
			<key>hash2</key>
			<data>
			1zcM/9rqtIKdUMSbdd+gdxgASTmlPeTaSee9lek0Ums=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Flutter</key>
		<dict>
			<key>hash</key>
			<data>
			Y5bNRHLLy5Ix0wN0uJlSsxfbHxI=
			</data>
			<key>hash2</key>
			<data>
			rS+Nj4X9LTstvbKnV8KdS6qQTiPxQGb+fR9Kzq0BNcU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash</key>
			<data>
			wTPJHICwW6wxY3b87ek7ITN5kJk=
			</data>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			zbvYFr9dywry0lMMrHuNOOaNgkY=
			</data>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash</key>
			<data>
			ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
			</data>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			V/wkSSsyYdMoexF6wPrC3KgkL4g=
			</data>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash</key>
			<data>
			vFsZXNqjflvqKqAzsIptQaTSJho=
			</data>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash</key>
			<data>
			sUgX1PJzkvyinL5i7nS1ro/Kd5o=
			</data>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash</key>
			<data>
			SpNs7IhIC7xP34Ej+LQCaEZkqik=
			</data>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash</key>
			<data>
			AqVvCbPmgWMQKrRnib05Okrjbp0=
			</data>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash</key>
			<data>
			bkw+DmHReHDg1PPcvmSjuLZrheA=
			</data>
			<key>hash2</key>
			<data>
			SqzvIxqBXEJ3U9LJ32hCEXsrH2P16gumQ+gQx6Pdlf4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash</key>
			<data>
			UqnnVWwQEYYX56eu7lt6dpR3LIc=
			</data>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			VjAwScWkWWSrDeetip3K4yhuwDU=
			</data>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			crQ9782ULebLQfIR+MbBkjB7d+k=
			</data>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash</key>
			<data>
			ocQVSiAiUMYfVtZIn48LpYTJA5w=
			</data>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			EARXud6pHb7ZYP8eXPDnluMqcXk=
			</data>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			qWHw5VIWEa0NmJ1PMhD16nlfRKk=
			</data>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash</key>
			<data>
			31prWLso2k5PfMMSbf5hGl+VE6Y=
			</data>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			LDr6kSVbUfyQFAxLwCACF5S2VEA=
			</data>
			<key>hash2</key>
			<data>
			yEgZTlCNrK/A/QBjEwNGB6ffC+A9gorPvnNgSbYuQ7Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			X2xbdmbtPYDLV7eeRvl3/zwaON8=
			</data>
			<key>hash2</key>
			<data>
			DtNp7a85dqmCAARl8dpbKa/0aA+1tosbQfbdRZw+mb4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wJV5dCKEGl+FAtDc8wJJh/fvKXs=
			</data>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			D+cqXttvC7E/uziGjFdqFabWd7A=
			</data>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			Y/repUi0ME4y+D1SK1TEmxPmfrM=
			</data>
			<key>hash2</key>
			<data>
			Yby50O9fmEEOVwi4gvGkbeU5vkgauDFwso8052Syx3s=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash</key>
			<data>
			ipm8hg7aB3LzsfShJfpNR0QQ4hw=
			</data>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
