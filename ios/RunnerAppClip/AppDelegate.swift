//
//  AppDelegate.swift
//  RunnerAppClip
//
//  Created by <PERSON> on 2025/6/6.
//

import UIKit
import Flutter

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    lazy var flutterEngine = FlutterEngine(name: "AppClipEngine")

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bo<PERSON> {
        print("🚀 Starting AppClip with Flutter")

        // Initialize Flutter engine for AppClip
        flutterEngine.run()

        // Register plugins for AppClip
        GeneratedPluginRegistrant.register(with: flutterEngine)

        // Set up method channel to tell Flutter this is an AppClip
        setupAppClipMethodChannel()

        print("✅ AppClip Flutter engine initialized")
        return true
    }

    private func setupAppClipMethodChannel() {
        let controller = FlutterViewController(engine: flutterEngine, nibName: nil, bundle: nil)
        let channel = FlutterMethodChannel(name: "card3.appclip", binaryMessenger: controller.binaryMessenger)

        channel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
            print("📱 AppClip method channel call: \(call.method)")
            if call.method == "isAppClip" {
                print("✅ Confirming AppClip mode to Flutter")
                result(true) // This is always true for AppClip
            } else {
                result(FlutterMethodNotImplemented)
            }
        }
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }


}

