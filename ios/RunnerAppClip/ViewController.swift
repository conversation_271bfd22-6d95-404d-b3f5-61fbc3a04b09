//
//  ViewController.swift
//  RunnerAppClip
//
//  Created by <PERSON> on 2025/6/6.
//

import UIKit

class ViewController: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()
        setupAppClipDemo()
    }

    private func setupAppClipDemo() {
        view.backgroundColor = .systemBackground

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 24
        stackView.alignment = .center
        stackView.translatesAutoresizingMaskIntoConstraints = false

        // Title
        let titleLabel = UILabel()
        titleLabel.text = "🚀 Card3 AppClip"
        titleLabel.font = UIFont.systemFont(ofSize: 32, weight: .bold)
        titleLabel.textAlignment = .center
        titleLabel.textColor = .systemBlue

        // Subtitle
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Flutter Module Ready!"
        subtitleLabel.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        subtitleLabel.textAlignment = .center
        subtitleLabel.textColor = .systemGreen

        // Description
        let descriptionLabel = UILabel()
        descriptionLabel.text = "✅ AppClip Target: Working\n✅ Flutter Module: Built\n✅ Integration: Ready\n\nThe Flutter 'clip' module is ready to show when the AppClip launches!"
        descriptionLabel.font = UIFont.systemFont(ofSize: 16)
        descriptionLabel.textAlignment = .center
        descriptionLabel.textColor = .label
        descriptionLabel.numberOfLines = 0

        // Demo button
        let demoButton = UIButton(type: .system)
        demoButton.setTitle("🎯 Show Flutter AppClip UI", for: .normal)
        demoButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        demoButton.backgroundColor = .systemBlue
        demoButton.setTitleColor(.white, for: .normal)
        demoButton.layer.cornerRadius = 12
        demoButton.contentEdgeInsets = UIEdgeInsets(top: 16, left: 24, bottom: 16, right: 24)
        demoButton.addTarget(self, action: #selector(showFlutterDemo), for: .touchUpInside)

        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(subtitleLabel)
        stackView.addArrangedSubview(descriptionLabel)
        stackView.addArrangedSubview(demoButton)

        view.addSubview(stackView)
        NSLayoutConstraint.activate([
            stackView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            stackView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            stackView.leadingAnchor.constraint(greaterThanOrEqualTo: view.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(lessThanOrEqualTo: view.trailingAnchor, constant: -20)
        ])
    }

    @objc private func showFlutterDemo() {
        let alert = UIAlertController(
            title: "🎉 Flutter AppClip Ready!",
            message: "Your Flutter 'clip' module is built and ready!\n\n✅ Material Design UI\n✅ Deep link handling\n✅ Riverpod state management\n✅ AppClip-optimized features\n\nThe Flutter module will show when the AppClip is properly launched with Flutter integration enabled.",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "Awesome! 🚀", style: .default))
        present(alert, animated: true)
    }

}

