//
//  ViewController.swift
//  RunnerAppClip
//
//  Created by <PERSON> on 2025/6/6.
//

import UIKit
import Flutter

class ViewController: UIViewController {

    private var isFlutterMode = false

    override func viewDidLoad() {
        super.viewDidLoad()
        setupAppClipView()
    }

    private func setupAppClipView() {
        // Create Flutter-inspired AppClip UI
        view.backgroundColor = .systemBackground

        let scrollView = UIScrollView()
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(scrollView)

        let contentView = UIView()
        contentView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(contentView)

        // Title
        let titleLabel = UILabel()
        titleLabel.text = "Card3 AppClip"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Subtitle
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Quick access to Card3 features"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16)
        subtitleLabel.textAlignment = .center
        subtitleLabel.textColor = .secondaryLabel
        subtitleLabel.numberOfLines = 0
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        // Feature description
        let descriptionLabel = UILabel()
        descriptionLabel.text = "This is a lightweight version of Card3 designed for quick access. Built with Flutter-inspired design principles."
        descriptionLabel.font = UIFont.systemFont(ofSize: 14)
        descriptionLabel.textAlignment = .center
        descriptionLabel.textColor = .secondaryLabel
        descriptionLabel.numberOfLines = 0
        descriptionLabel.translatesAutoresizingMaskIntoConstraints = false

        // Quick Scan Button (Flutter-style)
        let scanButton = createFlutterStyleButton(
            title: "Quick Scan",
            backgroundColor: .systemGreen,
            action: #selector(quickScan)
        )

        // Test AppClip Action Button
        let testButton = createFlutterStyleButton(
            title: "Test AppClip Action",
            backgroundColor: .systemBlue,
            action: #selector(testAppClipAction)
        )

        // Open Full App Button
        let openAppButton = createFlutterStyleButton(
            title: "Open Full Card3 App",
            backgroundColor: .systemIndigo,
            action: #selector(openFullApp)
        )

        // Add all views to content view
        [titleLabel, subtitleLabel, descriptionLabel, scanButton, testButton, openAppButton].forEach {
            contentView.addSubview($0)
        }

        // Set up constraints
        NSLayoutConstraint.activate([
            // Scroll view
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),

            // Content view
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),

            // Title
            titleLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 40),
            titleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Subtitle
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            subtitleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            subtitleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Description
            descriptionLabel.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 24),
            descriptionLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            descriptionLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Scan button
            scanButton.topAnchor.constraint(equalTo: descriptionLabel.bottomAnchor, constant: 40),
            scanButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            scanButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            scanButton.heightAnchor.constraint(equalToConstant: 56),

            // Test button
            testButton.topAnchor.constraint(equalTo: scanButton.bottomAnchor, constant: 16),
            testButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            testButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            testButton.heightAnchor.constraint(equalToConstant: 56),

            // Open app button
            openAppButton.topAnchor.constraint(equalTo: testButton.bottomAnchor, constant: 16),
            openAppButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            openAppButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            openAppButton.heightAnchor.constraint(equalToConstant: 56),
            openAppButton.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -40)
        ])
    }

    private func createFlutterStyleButton(title: String, backgroundColor: UIColor, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        button.backgroundColor = backgroundColor
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 12
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowOpacity = 0.1
        button.layer.shadowRadius = 4
        button.translatesAutoresizingMaskIntoConstraints = false
        button.addTarget(self, action: action, for: .touchUpInside)

        // Add touch animation
        button.addTarget(self, action: #selector(buttonTouchDown(_:)), for: .touchDown)
        button.addTarget(self, action: #selector(buttonTouchUp(_:)), for: [.touchUpInside, .touchUpOutside, .touchCancel])

        return button
    }

    @objc private func buttonTouchDown(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1) {
            sender.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }
    }

    @objc private func buttonTouchUp(_ sender: UIButton) {
        UIView.animate(withDuration: 0.1) {
            sender.transform = CGAffineTransform.identity
        }
    }

    @objc private func quickScan() {
        showActionDialog(title: "Quick Scan", message: "Opening QR/NFC scanner...\n\nThis would integrate with your Card3 scanning functionality.")
    }

    @objc private func testAppClipAction() {
        showActionDialog(title: "AppClip Action", message: "Testing AppClip functionality...\n\nURL: card3://scan\nAction: Quick scan mode\nStatus: Ready for integration")
    }

    @objc private func openFullApp() {
        // Try to open the full Card3 app
        if let url = URL(string: "card3://") {
            UIApplication.shared.open(url) { success in
                if !success {
                    DispatchQueue.main.async {
                        self.showActionDialog(title: "Opening App", message: "Transitioning to full Card3 app...\n\nThis would open the main Card3 application with full features.")
                    }
                }
            }
        }
    }

    private func showActionDialog(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        alert.addAction(UIAlertAction(title: "Open Full App", style: .default) { _ in
            self.openFullApp()
        })
        present(alert, animated: true)
    }
}

