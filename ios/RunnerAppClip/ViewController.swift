//
//  ViewController.swift
//  RunnerAppClip
//
//  Created by <PERSON> on 2025/6/6.
//

import UIKit

class ViewController: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()
        setupAppClipView()
    }

    private func setupAppClipView() {
        // Create a simple native AppClip UI for now
        view.backgroundColor = .systemBackground

        let titleLabel = UILabel()
        titleLabel.text = "Card3 AppClip"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        let subtitleLabel = UILabel()
        subtitleLabel.text = "Quick access to Card3 features"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16)
        subtitleLabel.textAlignment = .center
        subtitleLabel.textColor = .secondaryLabel
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        let openAppButton = UIButton(type: .system)
        openAppButton.setTitle("Open Full Card3 App", for: .normal)
        openAppButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        openAppButton.backgroundColor = .systemBlue
        openAppButton.setTitleColor(.white, for: .normal)
        openAppButton.layer.cornerRadius = 8
        openAppButton.translatesAutoresizingMaskIntoConstraints = false
        openAppButton.addTarget(self, action: #selector(openFullApp), for: .touchUpInside)

        let scanButton = UIButton(type: .system)
        scanButton.setTitle("Quick Scan", for: .normal)
        scanButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        scanButton.backgroundColor = .systemGreen
        scanButton.setTitleColor(.white, for: .normal)
        scanButton.layer.cornerRadius = 8
        scanButton.translatesAutoresizingMaskIntoConstraints = false
        scanButton.addTarget(self, action: #selector(quickScan), for: .touchUpInside)

        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        view.addSubview(openAppButton)
        view.addSubview(scanButton)

        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: view.centerYAnchor, constant: -100),

            subtitleLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),

            scanButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            scanButton.topAnchor.constraint(equalTo: subtitleLabel.bottomAnchor, constant: 40),
            scanButton.widthAnchor.constraint(equalToConstant: 200),
            scanButton.heightAnchor.constraint(equalToConstant: 50),

            openAppButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            openAppButton.topAnchor.constraint(equalTo: scanButton.bottomAnchor, constant: 16),
            openAppButton.widthAnchor.constraint(equalToConstant: 200),
            openAppButton.heightAnchor.constraint(equalToConstant: 50)
        ])
    }

    @objc private func openFullApp() {
        // Open the full Card3 app
        if let url = URL(string: "card3://") {
            UIApplication.shared.open(url)
        }
    }

    @objc private func quickScan() {
        // Handle quick scan action
        let alert = UIAlertController(title: "Quick Scan", message: "Scan functionality would be implemented here", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

