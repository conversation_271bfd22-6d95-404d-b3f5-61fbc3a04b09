//
//  ViewController.swift
//  RunnerAppClip
//
//  Created by <PERSON> on 2025/6/6.
//

import UIKit
import Flutter

class ViewController: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()
        setupFlutterView()
    }

    private func setupFlutterView() {
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else {
            return
        }

        let flutterViewController = FlutterViewController(
            engine: appDelegate.flutterEngine,
            nibName: nil,
            bundle: nil
        )

        // Add Flutter view controller as child
        addChild(flutterViewController)
        view.addSubview(flutterViewController.view)
        flutterViewController.didMove(toParent: self)

        // Set up constraints to fill the entire view
        flutterViewController.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            flutterViewController.view.topAnchor.constraint(equalTo: view.topAnchor),
            flutterViewController.view.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            flutterViewController.view.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            flutterViewController.view.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
}

