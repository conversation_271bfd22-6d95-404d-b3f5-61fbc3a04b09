import 'package:flutter/foundation.dart';

/// Shared AppLink handling logic between main app and AppClip
class AppLinkHandler {
  static const String cardScheme = 'card3';
  
  /// Parse and handle incoming URLs for AppClip
  static AppLinkAction? parseAppClipLink(Uri uri) {
    if (uri.scheme != cardScheme) {
      return null;
    }

    // Handle different AppClip actions based on the URL structure
    switch (uri.host) {
      case 'scan':
        return AppLinkAction.scan;
      case 'share':
        return AppLinkAction.share;
      case 'nfc':
        return AppLinkAction.nfc;
      case 'card':
        final cardId = uri.pathSegments.isNotEmpty ? uri.pathSegments.first : null;
        return AppLinkAction.viewCard(cardId);
      default:
        return AppLinkAction.home;
    }
  }

  /// Extract parameters from the URL
  static Map<String, String> extractParameters(Uri uri) {
    return uri.queryParameters;
  }

  /// Validate if the URL is suitable for AppClip handling
  static bool isAppClipCompatible(Uri uri) {
    // AppClip should handle lightweight actions only
    const compatibleActions = [
      'scan',
      'share',
      'nfc',
      'card',
    ];
    
    return uri.scheme == cardScheme && 
           compatibleActions.contains(uri.host);
  }
}

/// Enum representing different actions that can be triggered via AppLinks
enum AppLinkAction {
  home,
  scan,
  share,
  nfc,
  viewCard(String? cardId);

  const AppLinkAction.viewCard(this.cardId) : _type = 'viewCard';
  const AppLinkAction._(this._type) : cardId = null;

  static const AppLinkAction home = AppLinkAction._('home');
  static const AppLinkAction scan = AppLinkAction._('scan');
  static const AppLinkAction share = AppLinkAction._('share');
  static const AppLinkAction nfc = AppLinkAction._('nfc');

  final String _type;
  final String? cardId;

  @override
  String toString() {
    switch (_type) {
      case 'viewCard':
        return 'AppLinkAction.viewCard($cardId)';
      default:
        return 'AppLinkAction.$_type';
    }
  }
}
