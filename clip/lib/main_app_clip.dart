import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'shared/app_link_handler.dart';

void main() => runApp(const ProviderScope(child: Card3AppClip()));

class Card3AppClip extends StatelessWidget {
  const Card3AppClip({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Card3 Clip',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const AppClipHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class AppClipHomePage extends ConsumerStatefulWidget {
  const AppClipHomePage({super.key});

  @override
  ConsumerState<AppClipHomePage> createState() => _AppClipHomePageState();
}

class _AppClipHomePageState extends ConsumerState<AppClipHomePage> {
  String? _initialLink;
  String? _latestLink;

  @override
  void initState() {
    super.initState();
    _initAppLinks();
  }

  Future<void> _initAppLinks() async {
    // Simulate app links for now (will be implemented with proper plugin later)
    setState(() {
      _initialLink = 'card3://scan'; // Example initial link
    });
  }

  void _handleIncomingLink(String linkString) {
    final uri = Uri.tryParse(linkString);
    if (uri == null) return;

    debugPrint('Handling AppClip link: $uri');

    // Check if the link is compatible with AppClip
    if (!AppLinkHandler.isAppClipCompatible(uri)) {
      debugPrint('Link not compatible with AppClip, opening full app...');
      _openFullApp();
      return;
    }

    // Parse the action from the link
    final action = AppLinkHandler.parseAppClipLink(uri);
    final parameters = AppLinkHandler.extractParameters(uri);

    debugPrint('Parsed action: $action');
    debugPrint('Parameters: $parameters');

    // Handle the specific action
    _handleAppClipAction(action, parameters);
  }

  void _handleAppClipAction(AppLinkAction? action, Map<String, String> parameters) {
    if (action == null) return;

    switch (action) {
      case AppLinkAction.scan:
        _showActionDialog('Scan', 'Opening QR/NFC scanner...');
        break;
      case AppLinkAction.share:
        _showActionDialog('Share', 'Opening share functionality...');
        break;
      case AppLinkAction.nfc:
        _showActionDialog('NFC', 'Activating NFC reader...');
        break;
      case AppLinkAction.viewCard:
        final cardId = action.cardId;
        _showActionDialog('View Card', 'Loading card: ${cardId ?? 'unknown'}');
        break;
      case AppLinkAction.home:
      default:
        // Already on home, no action needed
        break;
    }
  }

  void _showActionDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _openFullApp();
            },
            child: const Text('Open Full App'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Card3 AppClip'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Welcome to Card3 AppClip!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This is a lightweight version of Card3 designed for quick access.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            if (_initialLink != null) ...[
              const Text(
                'Initial Link:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(_initialLink!),
              const SizedBox(height: 16),
            ],
            if (_latestLink != null) ...[
              const Text(
                'Latest Link:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(_latestLink!),
              const SizedBox(height: 16),
            ],
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  if (_initialLink != null) {
                    _handleIncomingLink(_initialLink!);
                  }
                },
                child: const Text('Test AppClip Action'),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // TODO: Add action to open full Card3 app
                  _openFullApp();
                },
                child: const Text('Open Full Card3 App'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openFullApp() {
    // TODO: Implement logic to open the full Card3 app
    // This could use URL schemes or other methods to transition
    debugPrint('Opening full Card3 app...');
  }
}
