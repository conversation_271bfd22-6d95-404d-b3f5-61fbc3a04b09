# Card3 AppClip Flutter Module

This directory contains a standalone Flutter module specifically designed for the Card3 AppClip integration.

## Overview

The AppClip module provides a lightweight, fast-loading version of Card3 functionality optimized for iOS App Clips. It shares common code with the main app while maintaining the 10MB size limit required for App Clips.

## Architecture

```
clip/
├── lib/
│   ├── main_app_clip.dart          # AppClip entry point
│   ├── shared/
│   │   └── app_link_handler.dart   # Shared URL handling logic
│   └── main.dart                   # Default module entry (not used)
├── pubspec.yaml                    # AppClip-specific dependencies
└── README.md                       # This file
```

## Features

- **Deep Link Handling**: Processes `card3://` URLs for AppClip actions
- **Lightweight UI**: Minimal Flutter UI optimized for quick loading
- **Shared Logic**: Reuses common models and services from the main app
- **App Transition**: Seamless handoff to the full Card3 app when needed

## Supported AppClip Actions

The AppClip handles the following URL schemes:

- `card3://scan` - Open QR/NFC scanner
- `card3://share` - Share functionality
- `card3://nfc` - NFC reader activation
- `card3://card/{id}` - View specific card

## Development

### Building the Module

```bash
# From the project root
./scripts/build_app_clip.sh
```

### Testing the Module Standalone

```bash
cd clip
flutter run
```
