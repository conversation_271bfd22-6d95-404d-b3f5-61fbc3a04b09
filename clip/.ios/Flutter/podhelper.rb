# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

require 'json'

# Install pods needed to embed Flutter application, Flutter engine, and plugins
# from the host application Podfile.
#
# @example
#   target 'MyApp' do
#     install_all_flutter_pods 'my_flutter'
#   end
# @param [String] flutter_application_path Path of the root directory of the Flutter module.
#                                          Optional, defaults to two levels up from the directory of this script.
#                                          MyApp/my_flutter/.ios/Flutter/../..
def install_all_flutter_pods(flutter_application_path = nil)
  # defined_in_file is a Pathname to the Podfile set by CocoaPods.
  pod_contents = File.read(defined_in_file)
  unless pod_contents.include? 'flutter_post_install'
    puts  <<~POSTINSTALL
Add `flutter_post_install(installer)` to your Podfile `post_install` block to build Flutter plugins:

post_install do |installer|
  flutter_post_install(installer)
end
POSTINSTALL
    raise 'Missing `flutter_post_install(installer)` in Podfile `post_install` block'
  end

  flutter_application_path ||= File.join('..', '..')
  install_flutter_engine_pod(flutter_application_path)
  install_flutter_plugin_pods(flutter_application_path)
  install_flutter_application_pod(flutter_application_path)
end

# Install Flutter engine pod.
#
# @example
#   target 'MyApp' do
#     install_flutter_engine_pod
#   end
def install_flutter_engine_pod(flutter_application_path = nil)
  flutter_application_path ||= File.join('..', '..')
  ios_application_path = File.join(flutter_application_path, '.ios')

  # flutter_install_ios_engine_pod is in Flutter root podhelper.rb
  flutter_install_ios_engine_pod(ios_application_path)
end

# Install Flutter plugin pods.
#
# @example
#   target 'MyApp' do
#     install_flutter_plugin_pods 'my_flutter'
#   end
# @param [String] flutter_application_path Path of the root directory of the Flutter module.
#                                          Optional, defaults to two levels up from the directory of this script.
#                                          MyApp/my_flutter/.ios/Flutter/../..
def install_flutter_plugin_pods(flutter_application_path)
  flutter_application_path ||= File.join('..', '..')
  ios_application_path = File.join(flutter_application_path, '.ios')
  # flutter_install_plugin_pods is in Flutter root podhelper.rb
  flutter_install_plugin_pods(ios_application_path, '.symlinks', 'ios')

  # Keep pod path relative so it can be checked into Podfile.lock.
  relative = flutter_relative_path_from_podfile(ios_application_path)
  pod 'FlutterPluginRegistrant', path: File.join(relative, 'Flutter', 'FlutterPluginRegistrant'), inhibit_warnings: true
end

# Install Flutter application pod.
#
# @example
#   target 'MyApp' do
#     install_flutter_application_pod '../flutter_settings_repository'
#   end
# @param [String] flutter_application_path Path of the root directory of the Flutter module.
#                                          Optional, defaults to two levels up from the directory of this script.
#                                          MyApp/my_flutter/.ios/Flutter/../..
def install_flutter_application_pod(flutter_application_path)
  flutter_application_path ||= File.join('..', '..')

  export_script_directory = File.join(flutter_application_path, '.ios', 'Flutter')

  # Keep script phase paths relative so they can be checked into source control.
  relative = flutter_relative_path_from_podfile(export_script_directory)

  flutter_export_environment_path = File.join('${SRCROOT}', relative, 'flutter_export_environment.sh')

  # Compile App.framework and move it and Flutter.framework to "BUILT_PRODUCTS_DIR"
  script_phase name: 'Run Flutter Build clip Script',
    script: "set -e\nset -u\nsource \"#{flutter_export_environment_path}\"\nexport VERBOSE_SCRIPT_LOGGING=1 && \"$FLUTTER_ROOT\"/packages/flutter_tools/bin/xcode_backend.sh build",
    execution_position: :before_compile

  # Embed App.framework AND Flutter.framework.
  script_phase name: 'Embed Flutter Build clip Script',
    script: "set -e\nset -u\nsource \"#{flutter_export_environment_path}\"\nexport VERBOSE_SCRIPT_LOGGING=1 && \"$FLUTTER_ROOT\"/packages/flutter_tools/bin/xcode_backend.sh embed_and_thin",
    execution_position: :after_compile
end

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', '..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first" unless File.exist?(generated_xcode_build_settings_path)

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT=(.*)/)
    return matches[1].strip if matches
  end
  # This should never happen...
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

# Run the post-install script to set build settings on Flutter plugins.
#
# @example
#   post_install do |installer|
#     flutter_post_install(installer)
#   end
# @param [Pod::Installer] installer Passed to the `post_install` block.
# @param [boolean] skip Do not change any build configurations. Set to suppress
#                       "Missing `flutter_post_install" exception.
def flutter_post_install(installer, skip: false)
  return if skip

  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |_build_configuration|
      # flutter_additional_ios_build_settings is in Flutter root podhelper.rb
      flutter_additional_ios_build_settings(target)
    end
  end
end
