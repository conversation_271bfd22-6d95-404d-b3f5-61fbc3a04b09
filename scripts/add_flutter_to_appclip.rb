#!/usr/bin/env ruby

require 'xcodeproj'

# Path to the Xcode project
project_path = 'ios/Runner.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# Find the AppClip target
appclip_target = project.targets.find { |target| target.name == 'RunnerAppClip' }

if appclip_target.nil?
  puts "❌ RunnerAppClip target not found"
  exit 1
end

puts "✅ Found RunnerAppClip target"

# Add Flutter framework paths
flutter_framework_path = 'Flutter/AppClip/Debug'
app_framework_path = 'Flutter/AppClip/Debug'

# Create framework references
flutter_framework_ref = project.new_file("#{flutter_framework_path}/Flutter.xcframework")
app_framework_ref = project.new_file("#{app_framework_path}/App.xcframework")

# Add frameworks to the target
appclip_target.frameworks_build_phase.add_file_reference(flutter_framework_ref)
appclip_target.frameworks_build_phase.add_file_reference(app_framework_ref)

# Add framework search paths
appclip_target.build_configurations.each do |config|
  config.build_settings['FRAMEWORK_SEARCH_PATHS'] ||= ['$(inherited)']
  config.build_settings['FRAMEWORK_SEARCH_PATHS'] << '$(PROJECT_DIR)/Flutter/AppClip/Debug'
  config.build_settings['FRAMEWORK_SEARCH_PATHS'] << '$(PROJECT_DIR)/Flutter/AppClip/Profile'
  config.build_settings['FRAMEWORK_SEARCH_PATHS'] << '$(PROJECT_DIR)/Flutter/AppClip/Release'
  
  # Add other Flutter settings
  config.build_settings['OTHER_LDFLAGS'] ||= ['$(inherited)']
  config.build_settings['OTHER_LDFLAGS'] << '-framework Flutter'
  config.build_settings['OTHER_LDFLAGS'] << '-framework App'
end

# Save the project
project.save

puts "✅ Added Flutter frameworks to RunnerAppClip target"
puts "✅ Updated build settings"
puts "🎉 AppClip is now configured to use Flutter!"
