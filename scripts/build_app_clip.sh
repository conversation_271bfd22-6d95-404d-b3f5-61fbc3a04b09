#!/bin/bash

# Build script for Card3 AppClip with Flutter module integration
# This script builds the Flutter module and prepares it for AppClip integration

set -e

echo "🚀 Building Card3 AppClip with Flutter module..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ] || [ ! -d "clip" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Build the main Flutter app
print_status "Building main Flutter app..."
flutter pub get
flutter build ios --no-codesign

# Step 2: Build the Flutter module for AppClip
print_status "Building Flutter module for AppClip..."
cd clip
flutter pub get

# Build the module as an iOS framework
flutter build ios-framework --output=../ios/Flutter/AppClip --no-codesign

cd ..

# Step 3: Copy necessary files for AppClip integration
print_status "Setting up AppClip integration files..."

# Create AppClip Flutter directory if it doesn't exist
mkdir -p ios/Flutter/AppClip

# Copy the built framework to the AppClip directory
if [ -d "ios/Flutter/AppClip/Debug" ]; then
    print_status "Flutter framework built successfully for AppClip"
else
    print_error "Failed to build Flutter framework for AppClip"
    exit 1
fi

# Step 4: Update Flutter target for AppClip
print_status "Configuring Flutter target for AppClip..."

# Create a custom xcconfig for AppClip
cat > ios/Flutter/AppClip-Custom.xcconfig << EOF
#include "Generated.xcconfig"

// Custom configuration for AppClip
FLUTTER_TARGET=package:clip/main_app_clip.dart
FLUTTER_APPLICATION_PATH=\$(SRCROOT)/../clip
EOF

print_status "✅ AppClip build preparation completed!"
print_warning "Next steps:"
echo "1. Open ios/Runner.xcworkspace in Xcode"
echo "2. Select the RunnerAppClip target"
echo "3. In Build Settings, set 'Flutter Target' to use the AppClip entry point"
echo "4. Build and test the AppClip"

print_status "🎉 Ready to build AppClip with Flutter integration!"
