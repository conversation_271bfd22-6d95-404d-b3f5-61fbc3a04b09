#!/bin/bash

# Build Flutter AppClip Script
# This script builds the Flutter app specifically for the AppClip target

set -e

echo "🚀 Building Flutter AppClip..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean
rm -rf ios/Flutter/AppClip

# Build Flutter for AppClip with the clip module as entry point
echo "📱 Building Flutter with AppClip entry point..."
flutter build ios --debug --simulator --target=lib/main_app_clip.dart

# Create AppClip-specific Flutter directory
echo "📁 Creating AppClip Flutter directory..."
mkdir -p ios/Flutter/AppClip/Debug
mkdir -p ios/Flutter/AppClip/Profile  
mkdir -p ios/Flutter/AppClip/Release

# Copy Flutter frameworks to AppClip directory
echo "📦 Copying Flutter frameworks for AppClip..."
cp -R build/ios/Debug-iphonesimulator/Flutter.framework ios/Flutter/AppClip/Debug/
cp -R build/ios/Debug-iphonesimulator/App.framework ios/Flutter/AppClip/Debug/

# Copy Flutter engine artifacts
echo "⚙️ Copying Flutter engine artifacts..."
cp -R ios/Flutter/Flutter.xcframework ios/Flutter/AppClip/Debug/ 2>/dev/null || true
cp -R ios/Flutter/App.xcframework ios/Flutter/AppClip/Debug/ 2>/dev/null || true

# Update AppClip xcconfig to use AppClip-specific frameworks
echo "🔧 Updating AppClip configuration..."
cat > ios/Flutter/AppClip-Debug.xcconfig << EOF
#include "Generated.xcconfig"
#include "Pods/Target Support Files/Pods-RunnerAppClip/Pods-RunnerAppClip.debug.xcconfig"

// AppClip specific configuration - use the clip module
FLUTTER_TARGET=lib/main_app_clip.dart
FRAMEWORK_SEARCH_PATHS=\$(inherited) \$(PROJECT_DIR)/Flutter/AppClip/Debug
EOF

cat > ios/Flutter/AppClip-Release.xcconfig << EOF
#include "Generated.xcconfig"
#include "Pods/Target Support Files/Pods-RunnerAppClip/Pods-RunnerAppClip.release.xcconfig"

// AppClip specific configuration - use the clip module
FLUTTER_TARGET=lib/main_app_clip.dart
FRAMEWORK_SEARCH_PATHS=\$(inherited) \$(PROJECT_DIR)/Flutter/AppClip/Release
EOF

echo "✅ Flutter AppClip build complete!"
echo "📱 You can now build the AppClip target in Xcode"
echo "🎯 The AppClip will show the Flutter clip module UI"
