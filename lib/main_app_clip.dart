import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

void main() => runApp(const ProviderScope(child: Card3AppClip()));

class Card3AppClip extends StatelessWidget {
  const Card3AppClip({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Card3 Clip',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const AppClipHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class AppClipHomePage extends ConsumerStatefulWidget {
  const AppClipHomePage({super.key});

  @override
  ConsumerState<AppClipHomePage> createState() => _AppClipHomePageState();
}

class _AppClipHomePageState extends ConsumerState<AppClipHomePage> {
  String? _initialLink;
  String? _latestLink;

  @override
  void initState() {
    super.initState();
    _initAppLinks();
  }

  Future<void> _initAppLinks() async {
    // Simulate app links for now (will be implemented with proper plugin later)
    setState(() {
      _initialLink = 'card3://scan'; // Example initial link
    });
  }

  void _handleIncomingLink(String linkString) {
    final uri = Uri.tryParse(linkString);
    if (uri == null) return;
    
    debugPrint('Handling AppClip link: $uri');
    
    // Parse the action from the link
    _handleAppClipAction(uri);
  }

  void _handleAppClipAction(Uri uri) {
    String action = uri.host;
    
    switch (action) {
      case 'scan':
        _showActionDialog('Scan', 'Opening QR/NFC scanner...');
        break;
      case 'share':
        _showActionDialog('Share', 'Opening share functionality...');
        break;
      case 'nfc':
        _showActionDialog('NFC', 'Activating NFC reader...');
        break;
      case 'card':
        final cardId = uri.pathSegments.isNotEmpty ? uri.pathSegments.first : 'unknown';
        _showActionDialog('View Card', 'Loading card: $cardId');
        break;
      default:
        // Already on home, no action needed
        break;
    }
  }

  void _showActionDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _openFullApp();
            },
            child: const Text('Open Full App'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Card3 AppClip'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Welcome to Card3 AppClip!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This is a lightweight Flutter version of Card3 designed for quick access.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            if (_initialLink != null) ...[
              const Text(
                'Initial Link:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(_initialLink!),
              const SizedBox(height: 16),
            ],
            if (_latestLink != null) ...[
              const Text(
                'Latest Link:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(_latestLink!),
              const SizedBox(height: 16),
            ],
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  if (_initialLink != null) {
                    _handleIncomingLink(_initialLink!);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Test AppClip Action'),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _openFullApp();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Open Full Card3 App'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openFullApp() {
    // TODO: Implement logic to open the full Card3 app
    // This could use URL schemes or other methods to transition
    debugPrint('Opening full Card3 app...');
    _showActionDialog('Opening App', 'Transitioning to full Card3 app...');
  }
}
