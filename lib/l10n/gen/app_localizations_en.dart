// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Card3';

  @override
  String textTakingLongerThanExpected(Object time) {
    return 'This takes longer than expected... (${time}s)';
  }

  @override
  String get titleBioVerify => 'Biometric Verification';

  @override
  String get textBioVerify =>
      'Set face/fingerprint/iris verification to unlock the app or send assets.';

  @override
  String get textPinProceed => 'Verify to proceed.';

  @override
  String get textUnlockRestrictPrompt =>
      'Too many attempts, please try again later';

  @override
  String get labelForgotPinButton => 'Forgot PIN code?';

  @override
  String get textIncorrectPinError => 'Incorrect PIN code.';

  @override
  String textWrongPinTimesLeft(Object time) {
    return '$time attempts remaining.';
  }

  @override
  String textWrongPinLastTimesLeft(Object timerCount) {
    return 'If incorrect, you can only try again after $timerCount.';
  }

  @override
  String get textPinUnlock => 'Verify to unlock your wallet.';

  @override
  String get labelNavDiscovery => 'Discovery';

  @override
  String get labelNavSocial => 'Social';

  @override
  String get labelNavWallet => 'Wallet';

  @override
  String get titleQrCodeInvalidPrompt => 'Invalid QR Code';

  @override
  String get textQrCodeInvalidPrompt => 'Please try scanning another QR code';

  @override
  String get textQrCodeErrorResolve =>
      'Aim at the QR code or adjust the distance.';

  @override
  String get textQrCodeErrorInvalid => 'QR code is not supported.';

  @override
  String get textQrCodeNotFound => 'QR code not found';

  @override
  String get labelSearchHint => 'Search CA/Token';

  @override
  String textLiveCACount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count CAs',
      one: '1 CA',
    );
    return '$_temp0';
  }

  @override
  String get textLiveCAMentioned => 'mentioned';

  @override
  String textChainNotYetSupported(Object chain) {
    return '$chain is not yet supported.';
  }
}
