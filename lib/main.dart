import 'dart:io' show HttpOverrides, Platform, exit;
import 'dart:isolate' show Isolate, RawReceivePort;

import 'package:app_links/app_links.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_misc/me_misc.dart' as misc;
import 'package:me_ui/me_ui.dart';
import 'package:me_utils/me_utils.dart';
import 'package:stack_trace/stack_trace.dart' as stack_trace;

import 'app.dart';
import 'constants/env.dart';
import 'constants/envs.dart' as envs;
import 'constants/release.dart';
import 'extensions/riverpod_extension.dart';
import 'feat/link/handler.dart';
import 'feat/link/helper.dart';
import 'firebase_options.dart';
import 'internals/box.dart';
import 'internals/methods.dart' as methods;
import 'internals/riverpod.dart';
import 'main_app_clip.dart';

void main() async {
  // Handling errors.
  FlutterError.onError = (details) {
    if (kDebugMode) {
      FlutterError.presentError(details);
    } else {
      methods.handleExceptions(details: details);
      FirebaseCrashlytics.instance.recordFlutterError(details);
    }
  };
  if (Release.sealed) {
    ErrorWidget.builder = (_) => const SizedBox.shrink();
  } else {
    MEErrorWidget.takeOver();
  }
  PlatformDispatcher.instance.onError = (e, s) {
    FirebaseCrashlytics.instance.recordError(e, s);
    methods.handleExceptions(error: e, stackTrace: s);
    return true;
  };
  Isolate.current.addErrorListener(
    RawReceivePort((pair) async {
      if (pair is! List) {
        return;
      }
      final List<String?> errorAndStacktrace = pair.cast<String?>();
      methods.handleExceptions(
        error: errorAndStacktrace.first,
        stackTrace: StackTrace.fromString(errorAndStacktrace.last ?? ''),
      );
    }).sendPort,
  );
  FlutterError.demangleStackTrace = (StackTrace stack) {
    if (stack is stack_trace.Trace) {
      return stack.vmTrace;
    }
    if (stack is stack_trace.Chain) {
      return stack.toTrace().vmTrace;
    }
    return stack;
  };

  WidgetsFlutterBinding.ensureInitialized();
  misc.hideKeyboard();
  Object? preparingException;
  StackTrace? preparingStackTrace;
  try {
    HttpOverrides.global = misc.MEHttpOverrides();
    ToastUtil.init();
    _configureBotUtil();
    LogUtil.enable = !Release.sealed;
    await Future.wait([
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge),
      DeviceUtil.initDeviceInfo(),
      PackageUtil.initInfo(
        buildTime: Release.buildTime,
        appVersionName: Release.versionName,
        appVersionCode: Release.versionCode,
      ),
      Boxes.init(),
      Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform),
      _configureWebView(),
    ]);
    AppLinkHelper.register();
    AppLinkHelper.addHandler(const LiveAppLinkHandler());

    // 调试：检查启动时的初始链接
    if (!Release.sealed) {
      try {
        final appLinks = AppLinks();
        final initialLink = await appLinks.getInitialLink();
        if (initialLink != null) {
          LogUtil.i('📱 检测到启动链接: $initialLink');
        } else {
          LogUtil.i('📱 没有启动链接');
        }
      } catch (e, s) {
        LogUtil.e(e, stackTrace: s);
      }
    }
  } catch (e, s) {
    preparingException = e;
    preparingStackTrace = s;
    if (e is RefCanceledException) {
      return;
    }
    await BotUtil.reportToBot(message: e, stackTrace: s);
  }
  if (preparingException != null) {
    if (kDebugMode) {
      final details = FlutterErrorDetails(
        exception: preparingException,
        stack: preparingStackTrace,
      );
      FlutterError.reportError(details);
      runApp(MEErrorWidget(details));
      return;
    }
    exit(1);
  }

  runApp(
    const ProviderScope(
      observers: kDebugMode ? [_riverpodLogger] : null,
      child: Card3App(),
    ),
  );
}

const _riverpodLogger = RiverpodLogger(
  loggingWhen: {
    RiverpodLoggingWhen.error,
  },
);

void _configureBotUtil() {
  BotUtil.url = Env.$active.BOT_UTIL_URL;
  BotUtil.key = Env.$active.BOT_UTIL_KEY;
  BotUtil.buildLogInfo = (buffer) {
    buffer.writeln(
      '[🧊]: ${Release.commitRef} '
      '(${Env.$active.env}${envs.isAuditing ? '-auditing' : ''})',
    );
    if (BoxService.getUserInfo() case final user?) {
      buffer.writeln('[🧑‍💻]: ${user.userEmail} (${user.name})');
    }
  };
}

Future<void> _configureWebView() async {
  PlatformInAppWebViewController.debugLoggingSettings.enabled = !Release.sealed;
  await InAppWebViewController.setJavaScriptBridgeName('card3_bridge');
  if (!Platform.isAndroid) {
    return;
  }
  await InAppWebViewController.setWebContentsDebuggingEnabled(!Release.sealed);
  final swAvailable = await WebViewFeature.isFeatureSupported(
    WebViewFeature.SERVICE_WORKER_BASIC_USAGE,
  );
  final swInterceptAvailable = await WebViewFeature.isFeatureSupported(
    WebViewFeature.SERVICE_WORKER_SHOULD_INTERCEPT_REQUEST,
  );
  if (swAvailable && swInterceptAvailable) {
    ServiceWorkerController.instance().setServiceWorkerClient(
      ServiceWorkerClient(
        shouldInterceptRequest: (request) async {
          LogUtil.d(
            'SW request: $request',
            tag: '🌐 WebView',
            tagWithTrace: false,
          );
          return null;
        },
      ),
    );
  }
}

/// Check if the app is running as an AppClip
Future<bool> _isAppClip() async {
  try {
    if (!Platform.isIOS) return false;

    // Check bundle identifier for AppClip
    // AppClip bundle IDs typically end with .Clip
    const bundleId = String.fromEnvironment('BUNDLE_ID', defaultValue: '');
    if (bundleId.contains('.Clip') || bundleId.contains('AppClip')) {
      print('✅ Detected AppClip mode via bundle ID: $bundleId');
      return true;
    }

    // Also check if we're running from AppClip target
    // This is a more reliable method for our setup
    try {
      const platform = MethodChannel('card3.appclip');
      final bool isAppClip = await platform.invokeMethod('isAppClip');
      if (isAppClip) {
        print('✅ Detected AppClip mode via method channel');
        return true;
      }
    } catch (e) {
      print('ℹ️ Method channel not available, checking other indicators');
    }

    // For our setup, we can also check if we're in the AppClip target
    // by looking at the app's bundle path or other indicators
    return false;
  } catch (e) {
    print('❌ Error checking AppClip mode: $e');
    return false;
  }
}

/// Check if the app was launched with AppClip URL
Future<bool> _isAppClipLaunch() async {
  try {
    // Check if launched with card3://appclip URL
    // This would be set by the AppClip when redirecting to main app

    // For now, we'll use a simple environment variable check
    // In a real implementation, you'd check the launch URL
    const appClipLaunch = String.fromEnvironment('APPCLIP_LAUNCH', defaultValue: '');
    if (appClipLaunch == 'true') {
      print('✅ Detected AppClip launch via environment');
      return true;
    }

    return false;
  } catch (e) {
    print('❌ Error checking AppClip launch: $e');
    return false;
  }
}
